<template>
  <div class="app-container">
    <el-card class="profile-card">
      <div slot="header" class="clearfix">
        <span>个人信息</span>
        <div style="float: right">
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-key"
            @click="showResetPwd = true"
            style="margin-right: 10px"
          >修改密码
          </el-button>
          <el-button
            v-if="!isEdit"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="isEdit = true"
          >编辑
          </el-button>
          <el-button
            v-else
            type="danger"
            size="mini"
            icon="el-icon-close"
            @click="cancelEdit"
          >取消
          </el-button>
        </div>
      </div>

      <div class="profile-content">
        <!-- 个人照片部分 -->
        <div class="avatar-section">
          <userAvatar v-model="form.avatar" :disabled="!isEdit" ref="userAvatar"/>
          <!-- 添加的提醒信息 -->
          <p style="color: #dd6161; font-size: 12px; margin-top: 10px;">请上传本人1寸白底照片</p>
        </div>

        <!-- 基本信息部分 -->
        <div class="info-section">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="120px"
            label-position="left"
            :disabled="!isEdit"
          >
            <el-row :gutter="40">
              <el-col :span="12">
                <h3 class="section-title">基本信息</h3>
                <el-form-item label="账号">
                  <el-input v-model="user.userName" readonly/>
                </el-form-item>
                <el-form-item label="姓名" prop="nickName">
                  <el-input v-model="form.nickName" maxlength="30"/>
                </el-form-item>
                <el-form-item label="身份证号" prop="IDNumber">
                  <el-input v-model="form.IDNumber" placeholder="请输入身份证号"/>
                </el-form-item>
                <el-form-item label="用户性别" prop="sex">
                  <el-select v-model="form.sex" placeholder="请选择性别">
                    <el-option
                      v-for="dict in dict.type.sys_user_sex"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="出生年月" prop="dateOfBirth">
                  <el-date-picker
                    v-model="form.dateOfBirth"
                    type="month"
                    placeholder="选择日期"
                    value-format="yyyy-MM"
                    format="yyyy年MM月"
                  />
                </el-form-item>
                <el-form-item label="民族" prop="nationality">
                  <el-select v-model="form.nationality" placeholder="请选择民族">
                    <el-option
                      v-for="item in nationOptions"
                      :key="item.label"
                      :label="item.label"
                      :value="item.label"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <h3 class="section-title">联系方式</h3>
                <el-form-item label="手机号码" prop="phonenumber">
                  <el-input v-model="form.phonenumber" maxlength="11"/>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="form.email" maxlength="50"/>
                </el-form-item>
                <!--                <el-form-item label="邮政编码" prop="mailingPostalCode">
                                  <el-input v-model="form.mailingPostalCode" placeholder="请输入邮政编码"/>
                                </el-form-item>-->
                <!--                <el-form-item label="监护人姓名" prop="guardian">-->
                <!--                  <el-input v-model="form.guardian" placeholder="请输入监护人" maxlength="10"/>-->
                <!--                </el-form-item>-->
                <!--                <el-form-item label="监护人电话" prop="guardianPhone">-->
                <!--                  <el-input v-model="form.guardianPhone" placeholder="请输入手机号码" maxlength="11"/>-->
                <!--                </el-form-item>-->
                <el-form-item label="邮寄地址" prop="mailingAddress">
                  <el-input v-model="form.mailingAddress" placeholder="请输入邮寄地址" maxlength="100"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="40">
              <el-col :span="12">
                <h3 class="section-title">学校信息</h3>
                <el-form-item label="学校名称" prop="school">
                  <el-input v-model="form.school" placeholder="请输入学校名称" maxlength="50"/>
                </el-form-item>
                <el-form-item label="学校区域" prop="schoolDistrict">
                  <el-cascader
                    style="width: 100%"
                    v-model="form.schoolDistrict"
                    :options="addressOptions.filter(opt => opt.id === '110000')"
                    :props="{ label: 'name', value: 'id' }"
                    ref="schoolDistrictCascader"
                    @change="handleSchoolDistrictChange"
                    placeholder="请选择学校所在区"
                  ></el-cascader>
                </el-form-item>
                <el-form-item label="学校地址" prop="schoolAddress">
                  <el-input v-model="form.schoolAddress" placeholder="请输入学校地址"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <div style="height: 62px; margin-bottom: 8px;"></div>
                <!--                <el-form-item label="学校邮箱" prop="schoolPostalCode">
                                  <el-input v-model="form.schoolPostalCode" placeholder="请输入学校邮箱" maxlength="50"/>
                                </el-form-item>
                                <el-form-item label="学校电话" prop="schoolPhone">
                                  <el-input v-model="form.schoolPhone" placeholder="请输入学校电话" maxlength="11"/>
                                </el-form-item>
                                -->
                <el-form-item label="学段" prop="grade">
                  <el-select v-model="form.grade" placeholder="请选择学段">
                    <el-option
                      v-for="dict in dict.type.grade"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="年级" prop="schoolManager" :rules="rules.schoolManager">
                  <el-select v-model="form.schoolManager" placeholder="请选择年级">
                    <el-option
                      v-for="option in gradeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

              </el-col>
            </el-row>

            <el-row :gutter="40">
              <el-col :span="24">
                <h3 class="section-title">父母信息</h3>
                <div v-if="form.grade !== '4'">
                  <div v-for="(parent, index) in form.parents" :key="index" class="parent-info-form">
                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item :label="index === 0 ? '父亲姓名' : '母亲姓名'" :prop="'parents.' + index + '.name'"
                                      :rules="rules.parentName">
                          <el-input v-model="parent.name" :placeholder="index === 0 ? '请输入父亲姓名' : '请输入母亲姓名'"
                                    maxlength="30"/>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="联系方式" :prop="'parents.' + index + '.phone'" :rules="rules.parentPhone">
                          <el-input v-model="parent.phone" placeholder="请输入联系方式" maxlength="11"/>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="工作单位" :prop="'parents.' + index + '.workplace'"
                                      :rules="rules.parentWorkplace">
                          <el-input v-model="parent.workplace" placeholder="请输入工作单位" maxlength="50"/>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-button v-if="isEdit && form.parents.length > 2" type="danger" size="mini" icon="el-icon-delete"
                               @click="removeParent(index)">删除
                    </el-button>
                  </div>
                  <el-button v-if="isEdit && form.parents.length < 2" type="primary" size="mini" icon="el-icon-plus"
                             @click="addParent">
                    新增父母信息
                  </el-button>
                </div>
                <div v-else>
                  <p class="parent-info-empty">教师用户无需填写父母信息</p>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="40">
              <el-col :span="24">
                <h3 class="section-title">团队信息</h3>
                <div v-for="(member, index) in form.teamMembers" :key="index" class="team-member-form">
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <el-form-item label="姓名" :prop="'teamMembers.' + index + '.name'" :rules="rules.teamMemberName">
                        <el-input v-model="member.name" maxlength="30" placeholder="请输入姓名"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="身份证号" :prop="'teamMembers.' + index + '.idNumber'"
                                    :rules="rules.teamMemberIDNumber">
                        <el-input v-model="member.idNumber" placeholder="请输入身份证号"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="手机号码" :prop="'teamMembers.' + index + '.phonenumber'"
                                    :rules="rules.teamMemberPhonenumber">
                        <el-input v-model="member.phonenumber" maxlength="11" placeholder="请输入手机号"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="邮箱" :prop="'teamMembers.' + index + '.email'"
                                    :rules="rules.teamMemberEmail">
                        <el-input v-model="member.email" maxlength="50" placeholder="请输入邮箱"/>
                      </el-form-item>
                    </el-col>
                    <!--                    <el-col :span="6">-->
                    <!--                      <el-form-item label="监护人" :prop="'teamMembers.' + index + '.guardian'"-->
                    <!--                                    :rules="rules.teamMemberGuardian">-->
                    <!--                        <el-input v-model="member.guardian" placeholder="请输入监护人姓名" maxlength="10"/>-->
                    <!--                      </el-form-item>-->
                    <!--                    </el-col>-->
                    <!--                    <el-col :span="6">-->
                    <!--                      <el-form-item label="监护电话" :prop="'teamMembers.' + index + '.guardianPhone'"-->
                    <!--                                    :rules="rules.teamMemberGuardianPhone">-->
                    <!--                        <el-input v-model="member.guardianPhone" placeholder="请输入监护人电话" maxlength="11"/>-->
                    <!--                      </el-form-item>-->
                    <!--                    </el-col>-->
                    <el-col :span="6">
                      <el-form-item label="学校名称" :prop="'teamMembers.' + index + '.school'"
                                    :rules="rules.teamMemberSchool">
                        <el-input v-model="member.school" placeholder="请输入学校名称" maxlength="50"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="学校区域" :prop="'teamMembers.' + index + '.schoolDistrict'"
                                    :rules="rules.teamMemberSchoolDistrict">
                        <el-cascader
                          v-model="member.schoolDistrict"
                          :options="addressOptions.filter(opt => opt.id === '110000')"
                          :props="{ label: 'name', value: 'id', expandTrigger: 'hover' }"
                          @change="(value) => handleTeamMemberSchoolDistrictChange(value, index)"
                          placeholder="请选择学校区域"
                          style="width: 100%"
                        ></el-cascader>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="学段" :prop="'teamMembers.' + index + '.grade'"
                                    :rules="rules.teamMemberGrade">
                        <el-select v-model="member.grade" placeholder="请选择学段" style="width: 100%" @change="() => handleTeamMemberGradeChange(member)">
                          <el-option
                            v-for="dict in dict.type.grade"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="年级" :prop="'teamMembers.' + index + '.gradeText'"
                                    :rules="rules.teamMemberGradeText">
                        <el-select v-model="member.gradeText" placeholder="请选择年级" style="width: 100%">
                          <el-option
                            v-for="option in getTeamMemberGradeOptions(member.grade)"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="学校地址" :prop="'teamMembers.' + index + '.schoolAddress'"
                                    :rules="rules.teamMemberSchoolAddress">
                        <el-input v-model="member.schoolAddress" placeholder="请输入学校地址" maxlength="100"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="性别" :prop="'teamMembers.' + index + '.sex'" :rules="rules.teamMemberSex">
                        <el-select v-model="member.sex" placeholder="请选择性别" style="width: 100%">
                          <el-option
                            v-for="dict in dict.type.sys_user_sex"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="民族" :prop="'teamMembers.' + index + '.nationality'"
                                    :rules="rules.teamMemberNationality">
                        <el-select v-model="member.nationality" placeholder="请选择民族" style="width: 100%">
                          <el-option
                            v-for="item in nationOptions"
                            :key="item.label"
                            :label="item.label"
                            :value="item.label"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="出生年月" :prop="'teamMembers.' + index + '.dateOfBirth'"
                                    :rules="rules.teamMemberDateOfBirth">
                        <el-date-picker
                          v-model="member.dateOfBirth"
                          type="month"
                          placeholder="选择日期"
                          style="width: 100%"
                          value-format="yyyy-MM"
                          format="yyyy年MM月"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="邮寄地址" :prop="'teamMembers.' + index + '.mailingAddress'"
                                    :rules="rules.teamMemberMailingAddress">
                        <el-input v-model="member.mailingAddress" placeholder="请输入邮寄地址" maxlength="100"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="父亲姓名" :prop="'teamMembers.' + index + '.fatherName'"
                                    :rules="rules.teamMemberFatherName">
                        <el-input v-model="member.fatherName" placeholder="请输入父亲姓名" maxlength="30"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="父亲电话" :prop="'teamMembers.' + index + '.fatherPhone'"
                                    :rules="rules.teamMemberFatherPhone">
                        <el-input v-model="member.fatherPhone" placeholder="请输入父亲电话" maxlength="11"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="父亲单位" :prop="'teamMembers.' + index + '.fatherWorkplace'"
                                    :rules="rules.teamMemberFatherWorkplace">
                        <el-input v-model="member.fatherWorkplace" placeholder="请输入父亲工作单位" maxlength="50"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="母亲姓名" :prop="'teamMembers.' + index + '.motherName'"
                                    :rules="rules.teamMemberMotherName">
                        <el-input v-model="member.motherName" placeholder="请输入母亲姓名" maxlength="30"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="母亲电话" :prop="'teamMembers.' + index + '.motherPhone'"
                                    :rules="rules.teamMemberMotherPhone">
                        <el-input v-model="member.motherPhone" placeholder="请输入母亲电话" maxlength="11"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="母亲单位" :prop="'teamMembers.' + index + '.motherWorkplace'"
                                    :rules="rules.teamMemberMotherWorkplace">
                        <el-input v-model="member.motherWorkplace" placeholder="请输入母亲工作单位" maxlength="50"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="个人照片" :prop="'teamMembers.' + index + '.avatar'"
                                    :rules="rules.teamMemberAvatar">
                        <div class="team-member-avatar">
                          <userAvatar
                            v-model="member.avatar"
                            :isTeamMember="true"
                            :memberId="member.idNumber || ''"
                            :disabled="!isEdit"
                            :ref="'teamMemberAvatar'"
                          />
                          <p class="avatar-tip">请上传1寸白底照片</p>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeTeamMember(index)">删除
                  </el-button>
                </div>
                <el-button v-if="isEdit" type="primary" size="mini" icon="el-icon-plus" @click="addTeamMember">
                  新增成员
                </el-button>
              </el-col>
            </el-row>

            <el-row v-if="isEdit" type="flex" justify="center" style="margin-top: 30px">
              <el-button type="primary" size="medium" @click="submit">保存</el-button>
              <el-button size="medium" @click="cancelEdit">取消</el-button>
            </el-row>
          </el-form>

          <div v-if="!isEdit" class="readonly-info">
            <h3 class="section-title">系统信息</h3>
            <div class="info-item">
              <span class="info-label">所属角色：</span>
              <span class="info-value">{{ roleGroup }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建日期：</span>
              <span class="info-value">{{ user.createTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="showResetPwd"
      width="500px"
      :close-on-click-modal="false"
    >
      <resetPwd v-if="showResetPwd" @success="handleResetPwdSuccess" @cancel="showResetPwd = false"/>
    </el-dialog>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import resetPwd from "./resetPwd";
import FileUpload from "@/components/FileUpload/index.vue";
import {getUserProfile, updateUserProfile, apiDownloadTemplate, apiGetAreaTreeList} from "@/api/system/user";
import {nationOptions} from "@/enums/commonEnum.js";
import store from "@/store";

export default {
  name: "Profile",
  dicts: ["sys_normal_disable", "sys_user_sex", "grade"],
  components: {userAvatar, resetPwd, FileUpload},
  data() {
    // 创建父母信息验证规则的动态验证函数
    const validateParentRequired = (rule, value, callback) => {
      if (this.form.grade === '4') {
        // 如果是教师，则父母信息为非必填
        callback();
      } else if (!value) {
        // 非教师且值为空，则提示必填
        callback(new Error(rule.message));
      } else {
        callback();
      }
    };

    return {
      isEdit: false,
      showResetPwd: false, // 控制修改密码对话框显示
      user: {},
      originalUser: {}, // 保存原始用户数据的副本
      form: {
        teamMembers: [],
        parents: []
      },
      tempParents: [], // 临时存储父母信息
      roleGroup: {},
      postGroup: {},
      nationOptions: nationOptions,
      // 年级选项
      gradeOptions: [],
      // 地区选项
      addressOptions: [],
      // 表单校验
      rules: {
        nickName: [
          {required: true, message: "用户昵称不能为空", trigger: "blur"}
        ],
        email: [
          {
            required: true,
            type: "email",
            message: "请输入正确的邮箱地址",
            pattern:
              /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ["blur", "change"]
          }
        ],
        /*schoolPostalCode: [
          {
            required: true,
            type: "email",
            message: "请输入正确的邮箱地址",
            pattern:
              /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ["blur", "change"]
          }
        ],*/
        phonenumber: [
          {
            required: true,
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        // guardianPhone: [
        //   {
        //     required: true,
        //     pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        //     message: "请输入正确的手机号码",
        //     trigger: "blur"
        //   }
        // ],
        IDNumber: [
          {
            required: true,
            pattern: /^[1-9]\d{5}(18|19|20|21|22|23)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/,
            message: '请输入正确的18位身份证号码',
            trigger: 'blur'
          }
        ],
        school: [
          {required: true, message: "请输入学校名称", trigger: "blur"}
        ],
        grade: [
          {
            required: true,
            message: "请选择学段",
            trigger: ["blur", "change"]
          }
        ],
        sex: [
          {required: true, message: "请选择性别", trigger: "change"}
        ],
        dateOfBirth: [
          {required: true, message: "请选择出生年月", trigger: "change"}
        ],
        nationality: [
          {required: true, message: "请选择民族", trigger: "change"}
        ],
        /* mailingPostalCode: [
           {required: true, message: "邮政编码不能为空", trigger: "blur"}
         ],*/
        mailingAddress: [
          {required: true, message: "邮寄地址不能为空", trigger: "blur"}
        ],
        // guardian: [
        //   {required: true, message: "请输入监护人姓名", trigger: "blur"}
        // ],
        schoolAddress: [
          {required: true, message: "请输入学校地址", trigger: "blur"}
        ],
        schoolDistrict: [
          {required: true, message: "请选择学校所在区", trigger: "change"}
        ],
        schoolManager: [
          {required: true, message: "请输入年级", trigger: ["blur", "change"]}
        ],
        /* schoolPhone: [
           {
             required: true,
             pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
             message: "请输入正确的学校电话",
             trigger: "blur"
           }
         ],
         schoolManager: [
           {required: true, message: "请输入学校负责人", trigger: "blur"}
         ],
         */

        // fileUrl: [
        //   {required: true, message: "请上传活动同意书", trigger: "change"}
        // ]

        // 团队信息校验
        teamMemberName: [
          {required: true, message: '姓名不能为空', trigger: 'blur'}
        ],
        teamMemberIDNumber: [
          {
            required: true,
            pattern: /^[1-9]\d{5}(18|19|20|21|22|23)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/,
            message: '请输入正确的18位身份证号码',
            trigger: 'blur'
          }
        ],
        teamMemberPhonenumber: [
          {
            required: true,
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        teamMemberEmail: [
          {
            required: true,
            type: 'email',
            message: '请输入正确的邮箱地址',
            pattern: /^([a-zA-Z0-9])+(([a-zA-Z0-9])|([._-][a-zA-Z0-9])*)+@([a-zA-Z0-9-])+((\.[a-zA-Z0-9-]{2,3}){1,2})$/,
            trigger: ['blur', 'change']
          }
        ],
        // teamMemberGuardian: [
        //   {required: true, message: '监护人不能为空', trigger: 'blur'}
        // ],
        // teamMemberGuardianPhone: [
        //   {
        //     required: true,
        //     pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        //     message: '请输入正确的手机号码',
        //     trigger: 'blur'
        //   }
        // ],
        teamMemberSchool: [
          {required: true, message: '请输入学校名称', trigger: 'blur'}
        ],
        teamMemberGrade: [
          {required: true, message: '请选择学段', trigger: ['blur', 'change']}
        ],
        teamMemberGradeText: [
          {required: true, message: '请输入年级', trigger: ['blur', 'change']}
        ],
        teamMemberAvatar: [
          {required: true, message: '请上传个人照片', trigger: 'change'}
        ],
        teamMemberSex: [
          {required: true, message: '请选择性别', trigger: 'change'}
        ],
        teamMemberNationality: [
          {required: true, message: '请选择民族', trigger: 'change'}
        ],
        teamMemberDateOfBirth: [
          {required: true, message: '请选择出生年月', trigger: 'change'}
        ],
        teamMemberMailingAddress: [
          {required: true, message: '请输入邮寄地址', trigger: 'blur'}
        ],
        teamMemberSchoolAddress: [
          {required: true, message: '请输入学校地址', trigger: 'blur'}
        ],
        teamMemberSchoolDistrict: [
          {required: true, message: '请选择学校所在区', trigger: 'change'}
        ],

        // 团队成员父母信息校验
        teamMemberFatherName: [
          {required: true, message: '请输入父亲姓名', trigger: 'blur'}
        ],
        teamMemberFatherPhone: [
          {
            required: true,
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        teamMemberFatherWorkplace: [
          {required: true, message: '请输入父亲工作单位', trigger: 'blur'}
        ],
        teamMemberMotherName: [
          {required: true, message: '请输入母亲姓名', trigger: 'blur'}
        ],
        teamMemberMotherPhone: [
          {
            required: true,
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ],
        teamMemberMotherWorkplace: [
          {required: true, message: '请输入母亲工作单位', trigger: 'blur'}
        ],

        // 父母信息校验
        parentName: [
          {validator: validateParentRequired, message: '姓名不能为空', trigger: 'blur'}
        ],
        parentPhone: [
          {
            validator: (rule, value, callback) => {
              if (this.form.grade === '4') {
                // 教师不校验必填
                callback();
              } else if (!value) {
                callback(new Error('手机号码不能为空'));
              } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
                callback(new Error('请输入正确的手机号码'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        parentWorkplace: [
          {validator: validateParentRequired, message: '工作单位不能为空', trigger: 'blur'}
        ],
      }

    };
  },
  created() {
    this.getUser();
    this.loadAddressOptions();
  },
  watch: {
    // 监听学段变化，当选择教师时，清空父母信息
    'form.grade': function(newVal, oldVal) {
      // 如果学段发生变化（不同的学段），清空年级字段
      if (newVal !== oldVal && oldVal !== undefined) {
        this.form.schoolManager = '';
      }

      if (newVal === '4') {
        // 如果是教师，且父母信息不为空，则保存父母信息并清空
        if (this.form.parents && this.form.parents.length > 0) {
          // 保存当前父母信息到临时变量
          this.tempParents = JSON.parse(JSON.stringify(this.form.parents));
          // 清空父母信息
          this.form.parents = [];
        }
      } else if (newVal !== '4') {
        // 如果不是教师
        if (this.tempParents && this.tempParents.length > 0) {
          // 如果有临时保存的父母信息，则恢复
          this.form.parents = JSON.parse(JSON.stringify(this.tempParents));
        } else if (!this.form.parents || this.form.parents.length === 0) {
          // 如果没有临时保存的父母信息，且当前父母信息为空，则初始化
          this.form.parents = [
            {name: '', phone: '', workplace: ''}, // 父亲信息
            {name: '', phone: '', workplace: ''}  // 母亲信息
          ];
        }
      }

      // 根据学段设置年级选项
      this.updateGradeOptions(newVal);
    }
  },
  methods: {
    // 根据学段更新年级选项
    updateGradeOptions(grade) {
      switch(grade) {
        case '1': // 小学
          this.gradeOptions = [
            { value: '一年级', label: '一年级' },
            { value: '二年级', label: '二年级' },
            { value: '三年级', label: '三年级' },
            { value: '四年级', label: '四年级' },
            { value: '五年级', label: '五年级' },
            { value: '六年级', label: '六年级' }
          ];
          break;
        case '2': // 普通中学/中专/职高
          this.gradeOptions = [
            { value: '初一', label: '初一' },
            { value: '初二', label: '初二' },
            { value: '初三', label: '初三' },
            { value: '高一', label: '高一' },
            { value: '高二', label: '高二' },
            { value: '高三', label: '高三' },
            { value: '中专', label: '中专' },
            { value: '职高', label: '职高' }
          ];
          break;
        case '3': // 高校本科及硕士研究生
          this.gradeOptions = [
            { value: '大一', label: '大一' },
            { value: '大二', label: '大二' },
            { value: '大三', label: '大三' },
            { value: '大四', label: '大四' },
            { value: '硕士研究生', label: '硕士研究生' }
          ];
          break;
        case '4': // 教师
          this.gradeOptions = [
            { value: '教师', label: '教师' }
          ];
          // 如果是教师，自动设置年级为"教师"（这里会覆盖之前的清空操作）
          this.form.schoolManager = '教师';
          break;
        default:
          this.gradeOptions = [];
      }
    },
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
        // 保存原始用户数据的深拷贝
        this.originalUser = JSON.parse(JSON.stringify(response.data));
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;

        // 合并数据，并确保 teamMembers 和 parents 存在
        this.form = {
          ...this.user,
          teamMembers: this.user.teamMembers || [],
          parents: this.user.parents || []
        };

        // 处理学校所在区数据，将字符串转换为数组
        if (this.form.schoolDistrict && typeof this.form.schoolDistrict === 'string') {
          this.form.schoolDistrict = this.form.schoolDistrict.split(',');
        }

        // 确保每个团队成员都有avatar属性和新增的属性
        if (this.form.teamMembers && this.form.teamMembers.length > 0) {
          // 创建一个新数组，确保Vue能检测到变化
          const updatedMembers = this.form.teamMembers.map(member => {
            // 处理团队成员的学校所在区数据，将字符串转换为数组
            if (member.schoolDistrict && typeof member.schoolDistrict === 'string') {
              member.schoolDistrict = member.schoolDistrict.split(',');
            }

            // 返回一个新对象，包含所有必要的字段
            return {
              ...member,
              avatar: member.avatar || '',
              sex: member.sex || '',
              nationality: member.nationality || '',
              dateOfBirth: member.dateOfBirth || null,
              mailingAddress: member.mailingAddress || '',
              schoolAddress: member.schoolAddress || '',
              fatherName: member.fatherName || '',
              fatherPhone: member.fatherPhone || '',
              fatherWorkplace: member.fatherWorkplace || '',
              motherName: member.motherName || '',
              motherPhone: member.motherPhone || '',
              motherWorkplace: member.motherWorkplace || '',
              gradeText: member.gradeText || '',
              schoolDistrict: member.schoolDistrict || [] // 确保schoolDistrict存在
            };
          });

          // 替换原数组
          this.form.teamMembers = updatedMembers;
        }

        // 确保父母信息存在
        if (!Array.isArray(this.form.parents) || this.form.parents.length === 0) {
          // 初始化父母信息，默认添加父亲和母亲
          if (this.form.grade !== '4') { // 非教师用户才初始化父母信息
            this.form.parents = [
              {name: '', phone: '', workplace: ''}, // 父亲信息
              {name: '', phone: '', workplace: ''}  // 母亲信息
            ];
          }
        } else if (this.form.parents.length === 1) {
          // 如果只有一条记录，添加第二条
          this.form.parents.push({name: '', phone: '', workplace: ''});
        }

        // 清空临时存储的父母信息
        this.tempParents = [];

        // 确保个人照片字段存在
        if (!this.form.avatar && this.user.avatar) {
          this.form.avatar = this.user.avatar;
        }

        // 如果有个人照片，更新store中的个人照片
        if (this.form.avatar) {
          store.commit('SET_AVATAR', this.form.avatar);
        }

        // 根据学段更新年级选项
        this.updateGradeOptions(this.form.grade);

        // 强制Vue重新渲染
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      });
    },
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 检查团队成员身份证号是否重复
          if (!this.checkDuplicateIDNumber()) {
            return;
          }

          // 验证个人头像是否上传
          const userAvatarRef = this.$refs.userAvatar;
          if (userAvatarRef && userAvatarRef.isDefaultAvatar) {
            this.$modal.msgError("请上传个人照片");
            return;
          }

          // 验证团队成员头像是否上传
          if (this.form.teamMembers && this.form.teamMembers.length > 0) {
            // 获取所有团队成员头像组件引用
            const teamMemberAvatarRefs = this.$refs.teamMemberAvatar;
            if (teamMemberAvatarRefs) {
              // 可能是单个引用或引用数组
              const avatarRefs = Array.isArray(teamMemberAvatarRefs) ? teamMemberAvatarRefs : [teamMemberAvatarRefs];

              // 检查每个成员是否上传了头像
              for (let i = 0; i < avatarRefs.length; i++) {
                const avatarRef = avatarRefs[i];
                if (avatarRef && avatarRef.isDefaultAvatar) {
                  this.$modal.msgError(`请上传第${i + 1}个团队成员的照片`);
                  return;
                }
              }
            }
          }

          // 准备提交数据
          const submitData = {
            ...this.form,
            avatar: this.form.avatar || this.user.avatar // 确保个人照片字段存在
          };

          // 处理学校所在区数据，转换为字符串格式
          if (submitData.schoolDistrict && Array.isArray(submitData.schoolDistrict)) {
            submitData.schoolDistrict = submitData.schoolDistrict.join(',');
          }

          // 如果是教师，清空父母信息
          if (submitData.grade === '4') {
            submitData.parents = [];
          }

          // 处理个人出生年月日期字段
          if (submitData.dateOfBirth instanceof Date) {
            // 使用本地时间避免时区问题
            const year = submitData.dateOfBirth.getFullYear();
            const month = submitData.dateOfBirth.getMonth() + 1;
            submitData.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
          }
          // 如果dateOfBirth已经是字符串格式，确保格式正确
          else if (typeof submitData.dateOfBirth === 'string' && submitData.dateOfBirth) {
            // 如果格式不是YYYY-MM，尝试转换
            if (!/^\d{4}-\d{2}$/.test(submitData.dateOfBirth)) {
              const date = new Date(submitData.dateOfBirth);
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                submitData.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
              }
            }
          }

          // 处理团队成员日期字段，确保正确序列化
          if (submitData.teamMembers && submitData.teamMembers.length > 0) {
            submitData.teamMembers = submitData.teamMembers.map(member => {
              // 如果日期是Date对象，转换为字符串
              if (member.dateOfBirth instanceof Date) {
                // 使用本地时间避免时区问题
                const year = member.dateOfBirth.getFullYear();
                const month = member.dateOfBirth.getMonth() + 1;
                member.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
              }
              // 如果dateOfBirth已经是字符串格式，确保格式正确
              else if (typeof member.dateOfBirth === 'string' && member.dateOfBirth) {
                // 如果格式不是YYYY-MM，尝试转换
                if (!/^\d{4}-\d{2}$/.test(member.dateOfBirth)) {
                  const date = new Date(member.dateOfBirth);
                  if (!isNaN(date.getTime())) {
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    member.dateOfBirth = `${year}-${String(month).padStart(2, '0')}`;
                  }
                }
              }

              // 处理团队成员的学校所在区数据
              if (member.schoolDistrict && Array.isArray(member.schoolDistrict)) {
                member.schoolDistrict = member.schoolDistrict.join(',');
              }

              return member;
            });
          }

          updateUserProfile(submitData).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.isEdit = false;
            this.getUser(); // 重新获取用户数据，更新原始数据
          });
        }
      });
    },
    cancelEdit() {
      this.isEdit = false;
      // 从原始数据恢复，而不是从可能已经被修改的 user 对象
      this.form = JSON.parse(JSON.stringify(this.originalUser));

      // 处理学校所在区数据，将字符串转换为数组
      if (this.form.schoolDistrict && typeof this.form.schoolDistrict === 'string') {
        this.form.schoolDistrict = this.form.schoolDistrict.split(',');
      }

      // 确保teamMembers存在
      if (!this.form.teamMembers) {
        this.form.teamMembers = [];
      } else {
        // 创建一个新数组，确保Vue能检测到变化
        const updatedMembers = this.form.teamMembers.map(member => {
          // 处理团队成员的学校所在区数据，将字符串转换为数组
          if (member.schoolDistrict && typeof member.schoolDistrict === 'string') {
            member.schoolDistrict = member.schoolDistrict.split(',');
          }

          // 返回一个新对象，包含所有必要的字段
          return {
            ...member,
            avatar: member.avatar || '',
            sex: member.sex || '',
            nationality: member.nationality || '',
            dateOfBirth: member.dateOfBirth || null,
            mailingAddress: member.mailingAddress || '',
            schoolAddress: member.schoolAddress || '',
            fatherName: member.fatherName || '',
            fatherPhone: member.fatherPhone || '',
            fatherWorkplace: member.fatherWorkplace || '',
            motherName: member.motherName || '',
            motherPhone: member.motherPhone || '',
            motherWorkplace: member.motherWorkplace || '',
            gradeText: member.gradeText || '',
            schoolDistrict: member.schoolDistrict || [] // 确保schoolDistrict存在
          };
        });

        // 替换原数组
        this.form.teamMembers = updatedMembers;
      }

      // 确保父母信息存在
      if (!Array.isArray(this.form.parents) || this.form.parents.length === 0) {
        this.form.parents = [];
      }

      // 清空临时存储的父母信息
      this.tempParents = [];

      // 更新年级选项
      this.updateGradeOptions(this.form.grade);

      // 强制Vue重新渲染
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    // 下载同意书
    async importTemplateFile() {
      const {code, data} = await apiDownloadTemplate();
      if (code == 200 && data) {
        const cleanedUrl = new URL(data, window.location.href).href;
        const a = document.createElement("a");
        a.href = cleanedUrl;
        a.target = "_blank";
        a.download = "活动同意书模版";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },
    handleChangeFile(value, props) {
      this.form[props] = value;
    },

    // 团队信息
    addTeamMember() {
      if (this.form.teamMembers.length < 2) {
        // 检查是否存在重复的身份证号
        const newMember = {
          name: '',
          idNumber: '',
          phonenumber: '',
          email: '',
          guardian: '',
          guardianPhone: '',
          school: '',
          grade: '',
          gradeText: '',
          avatar: '',
          sex: '',
          nationality: '',
          dateOfBirth: null, // 使用null而不是空字符串
          mailingAddress: '',
          schoolAddress: '',
          fatherName: '',
          fatherPhone: '',
          fatherWorkplace: '',
          motherName: '',
          motherPhone: '',
          motherWorkplace: ''
        };
        this.form.teamMembers.push(newMember);
      } else {
        this.$message.warning('最多只能添加两名团队成员');
      }
    },

    removeTeamMember(index) {
      this.form.teamMembers.splice(index, 1);
    },

    // 监听团队成员学段变化
    handleTeamMemberGradeChange(member) {
      // 如果是教师，自动设置年级为"教师"
      if (member.grade === '4') {
        member.gradeText = '教师';
      } else if (member.gradeText) {
        // 如果已有年级值，检查是否与当前学段匹配
        const gradeOptions = this.getTeamMemberGradeOptions(member.grade);
        const validValues = gradeOptions.map(option => option.value);
        if (!validValues.includes(member.gradeText)) {
          // 如果不匹配，清空年级值
          member.gradeText = '';
        }
      }
    },

    // 检查团队成员身份证号是否重复
    checkDuplicateIDNumber() {
      const idNumbers = this.form.teamMembers.map(member => member.idNumber).filter(id => id);
      const uniqueIdNumbers = new Set(idNumbers);

      // 如果去重后长度不一致，说明有重复
      if (idNumbers.length !== uniqueIdNumbers.size) {
        this.$modal.msgError('团队成员身份证号重复');
        return false;
      }
      return true;
    },

    // 父母信息
    addParent() {
      if (this.form.parents.length < 2) {
        const newParent = {
          name: '',
          phone: '',
          workplace: ''
        };
        this.form.parents.push(newParent);
      } else {
        this.$message.warning('最多只能添加两条父母信息');
      }
    },

    removeParent(index) {
      this.form.parents.splice(index, 1);
    },

    // 获取团队成员的年级选项
    getTeamMemberGradeOptions(grade) {
      switch(grade) {
        case '1': // 小学
          return [
            { value: '一年级', label: '一年级' },
            { value: '二年级', label: '二年级' },
            { value: '三年级', label: '三年级' },
            { value: '四年级', label: '四年级' },
            { value: '五年级', label: '五年级' },
            { value: '六年级', label: '六年级' }
          ];
        case '2': // 普通中学/中专/职高
          return [
            { value: '初一', label: '初一' },
            { value: '初二', label: '初二' },
            { value: '初三', label: '初三' },
            { value: '高一', label: '高一' },
            { value: '高二', label: '高二' },
            { value: '高三', label: '高三' },
            { value: '中专', label: '中专' },
            { value: '职高', label: '职高' }
          ];
        case '3': // 高校本科及硕士研究生
          return [
            { value: '大一', label: '大一' },
            { value: '大二', label: '大二' },
            { value: '大三', label: '大三' },
            { value: '大四', label: '大四' },
            { value: '硕士研究生', label: '硕士研究生' }
          ];
        case '4': // 教师
          return [
            { value: '教师', label: '教师' }
          ];
        default:
          return [];
      }
    },

    loadAddressOptions() {
      apiGetAreaTreeList().then((response) => {
        if (response.code === 200) {
          this.addressOptions = response.data;
        }
      });
    },

    // 处理学校所在区变化
    handleSchoolDistrictChange() {
      if (this.$refs.schoolDistrictCascader) {
        const nodes = this.$refs.schoolDistrictCascader.getCheckedNodes();
        if (nodes.length > 0) {
          this.form.schoolDistrictText = nodes[0].pathNodes.map(item => item.label).join('/');
        }
      }
    },

    // 处理团队成员学校所在区变化
    handleTeamMemberSchoolDistrictChange(value, index) {
      if (value && value.length > 0) {
        // 查找选中的节点路径
        let pathText = '';
        let currentOptions = this.addressOptions.filter(opt => opt.id === '110000');

        for (let i = 0; i < value.length; i++) {
          const id = value[i];
          const option = currentOptions.find(opt => opt.id === id);

          if (option) {
            pathText += (pathText ? '/' : '') + option.name;
            currentOptions = option.children || [];
          }
        }

        // 保存路径文本和值
        this.form.teamMembers[index].schoolDistrictText = pathText;
      }
    },

    // 处理修改密码成功
    handleResetPwdSuccess() {
      this.showResetPwd = false;
      this.$modal.msgSuccess("密码修改成功");
    },

  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.profile-card {
  margin: 0 auto;
  max-width: 1200px;
}

.profile-content {
  display: flex;
  flex-wrap: wrap;
}

.avatar-section {
  width: 200px;
  padding: 20px;
  text-align: center;
}

.info-section {
  flex: 1;
  min-width: 300px;
  padding: 0 20px;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #409eff;
}

.readonly-info {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.info-item {
  margin-bottom: 15px;
  font-size: 14px;
}

.info-label {
  display: inline-block;
  width: 100px;
  color: #666;
}

.info-value {
  color: #333;
}

::v-deep .el-select,
.el-date-editor {
  width: 100% !important;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
  }

  .avatar-section {
    width: 100%;
    margin-bottom: 20px;
  }
}

.team-member-form {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 15px;

  // 缩小 label 宽度并调整输入框宽度
  ::v-deep .el-form-item__content {
    margin-left: 80px !important; // 缩小 label 占用空间
  }

  ::v-deep .el-form-item__label {
    width: 80px !important; // label 宽度
    font-size: 13px;
  }

  // 输入框拉满容器
  ::v-deep .el-input__inner {
    width: 100%;
  }

  // 团队成员个人照片样式
  .team-member-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;

    ::v-deep .avatar-uploader {
      width: 100px;
      height: 100px;

      .el-upload {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
      }

      .user-info-head {
        width: 100px;
        height: 120px;
        margin: 0 auto;
      }
    }

    .avatar-tip {
      color: #dd6161;
      font-size: 12px;
      margin-top: 5px;
      text-align: center;
    }
  }
}

.parent-info-form {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 15px;

  ::v-deep .el-form-item__content {
    margin-left: 80px !important; // 缩小 label 占用空间
  }

  ::v-deep .el-form-item__label {
    width: 80px !important; // label 宽度
    font-size: 13px;
  }

  ::v-deep .el-input__inner {
    width: 100%;
  }
}

.parent-info-empty {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px;
  background-color: #f8f8f8;
  border-radius: 4px;
  margin-bottom: 15px;
}

</style>
