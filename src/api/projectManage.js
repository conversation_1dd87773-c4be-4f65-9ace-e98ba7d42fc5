import request from '@/utils/request'


// 获取项目列表
export function apiGetProjectList(params) {
    return request({
        url: '/projectInfo/queryByPage',
        method: 'get',
        params
    })
}

// 新增
export function apiAddProject(data) {
    return request({
        url: '/projectInfo/add',
        method: 'post',
        data
    })
}

// 修改
export function apiUpdateProject(data) {
    return request({
        url: '/projectInfo/update',
        method: 'post',
        data
    })
}

// 删除
export function apiDeleteProject(id) {
    return request({
        url: '/projectInfo/delete/' + id,
        method: 'delete',
    })
}

// 获取详情
export function apiGetProjectDetail(id) {
    return request({
        url: '/projectInfo/queryById',
        method: 'get',
        params: { id }
    })
}

/**
 * 下载创作者声明模版
 */
export function apiDownloadTemplate(idFlag) {
    return request({
        url: '/projectInfo/downResult',
        method: 'get',
        params: { idFlag }
    })
}

/**
 * 下载申报表简单模版
 */
export function apiDownDeclaration() {
  return request({
    url: '/projectInfo/downDeclaration',
    method: 'get'
  })
}

/**
 * 导出申报表
 */
export function apiDownByProjectId(projectId) {
  return request({
    url: '/projectInfo/downByProjectId',
    method: 'get',
    params: { projectId },
    responseType: 'blob'
  })
}

// 申报
export function apiDeclarationProject(data) {
  return request({
    url: '/projectInfo/declaration',
    method: 'post',
    data
  })
}

// 审核
export function apiAuditProject(data) {
  return request({
    url: '/projectInfo/audit',
    method: 'post',
    data
  })
}

// 推荐
export function apiRecommendProject(data) {
  return request({
    url: '/projectInfo/recommend',
    method: 'post',
    data
  })
}

/**
 * 批量导出项目
 */
export function apiExportProjects(ids) {
  return request({
    url: '/projectInfo/export',
    method: 'post',
    data: ids,
    responseType: 'blob'
  })
}

/**
 * 获取项目评分信息
 */
export function apiGetProjectScore(projectId) {
  return request({
    url: `/projectInfo/score/${projectId}`,
    method: 'get'
  })
}

/**
 * 提交评分
 */
export function apiSubmitScore(data) {
  return request({
    url: '/projectInfo/score',
    method: 'post',
    data
  })
}

/**
 * 获取默认评语
 */
export function apiGetDefaultComment() {
  return request({
    url: '/projectInfo/score/getDefaultComment',
    method: 'get'
  })
}

/**
 * 检查当前用户是否已经对指定项目进行过评分
 */
export function apiCheckJudgeScored(projectId) {
  return request({
    url: `/projectInfo/score/checkScored/${projectId}`,
    method: 'get'
  })
}
